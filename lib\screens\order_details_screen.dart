import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/types.dart';
import '../state/app_state.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  const OrderDetailsScreen({super.key, required this.order});
  final OrderModel order;

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  late OrderStatus status;

  @override
  void initState() {
    super.initState();
    status = widget.order.status;
  }

  @override
  Widget build(BuildContext context) {
    final o = widget.order;
    return Scaffold(
      appBar: AppBar(title: const Text('Order Details')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: ListTile(
              leading: const Icon(Icons.smartphone),
              title: Text(o.deviceModel),
              subtitle: Text(o.brand ?? ''),
              trailing: Text('₹${o.estimatedCost}'),
            ),
          ),
          const SizedBox(height: 12),
          Card(
            child: ListTile(
              leading: const Icon(Icons.person),
              title: Text(o.customer.name),
              subtitle: Text(o.customer.phone),
              trailing: Wrap(spacing: 8, children: [
                IconButton(
                  icon: const Icon(Icons.call, color: Colors.green),
                  onPressed: () => _launchUrl(Uri.parse('tel:${o.customer.phone}')),
                ),
                IconButton(
                  icon: const Icon(Icons.message, color: Colors.green),
                  onPressed: () => _launchUrl(Uri.parse('https://wa.me/${o.customer.phone}')),
                ),
              ]),
            ),
          ),
          const SizedBox(height: 12),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Issue', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Text(o.issueDescription),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<OrderStatus>(
            value: status,
            decoration: const InputDecoration(labelText: 'Update Status', border: OutlineInputBorder()),
            items: OrderStatus.values
                .map((s) => DropdownMenuItem(value: s, child: Text(s.name)))
                .toList(),
            onChanged: (s) => setState(() => status = s ?? status),
          ),
          const SizedBox(height: 16),
          FilledButton(
            onPressed: _save,
            child: const Text('Save'),
          )
        ],
      ),
    );
  }

  Future<void> _save() async {
    final updated = widget.order.copyWith(status: status, updatedAt: DateTime.now());
    await ref.read(ordersProvider.notifier).update(updated);
    if (!mounted) return;
    Navigator.of(context).pop(updated);
  }

  Future<void> _launchUrl(Uri uri) async {
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Could not launch')));
    }
  }
}


