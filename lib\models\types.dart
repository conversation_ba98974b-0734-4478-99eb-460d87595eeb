enum OrderStatus {
  received,
  diagnosing,
  repairInProgress,
  qualityCheck,
  readyForDelivery,
  delivered,
  cancelled,
}

enum JobPriority { normal, urgent, vip }

class Customer {
  final String id;
  final String name;
  final String phone;
  final String? address;
  final String? notes;
  final String? avatarUrl;

  const Customer({
    required this.id,
    required this.name,
    required this.phone,
    this.address,
    this.notes,
    this.avatarUrl,
  });

  Map<String, dynamic> toMap() => {
        'id': id,
        'name': name,
        'phone': phone,
        'address': address,
        'notes': notes,
        'avatarUrl': avatarUrl,
      };

  factory Customer.fromMap(Map<String, dynamic> map) => Customer(
        id: map['id'] as String,
        name: map['name'] as String,
        phone: map['phone'] as String,
        address: map['address'] as String?,
        notes: map['notes'] as String?,
        avatarUrl: map['avatarUrl'] as String?,
      );
}

class OrderModel {
  final String id;
  final Customer customer;
  final String deviceModel;
  final String? brand;
  final String issueDescription;
  final int estimatedCost;
  final int advancePayment;
  final OrderStatus status;
  final JobPriority priority;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? photoUrl;
  final String? technicianId;

  const OrderModel({
    required this.id,
    required this.customer,
    required this.deviceModel,
    this.brand,
    required this.issueDescription,
    required this.estimatedCost,
    required this.advancePayment,
    required this.status,
    required this.priority,
    required this.createdAt,
    required this.updatedAt,
    this.photoUrl,
    this.technicianId,
  });

  OrderModel copyWith({
    Customer? customer,
    String? deviceModel,
    String? brand,
    String? issueDescription,
    int? estimatedCost,
    int? advancePayment,
    OrderStatus? status,
    JobPriority? priority,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? photoUrl,
    String? technicianId,
  }) {
    return OrderModel(
      id: id,
      customer: customer ?? this.customer,
      deviceModel: deviceModel ?? this.deviceModel,
      brand: brand ?? this.brand,
      issueDescription: issueDescription ?? this.issueDescription,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      advancePayment: advancePayment ?? this.advancePayment,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      photoUrl: photoUrl ?? this.photoUrl,
      technicianId: technicianId ?? this.technicianId,
    );
  }

  Map<String, dynamic> toMap() => {
        'id': id,
        'customer': customer.toMap(),
        'deviceModel': deviceModel,
        'brand': brand,
        'issueDescription': issueDescription,
        'estimatedCost': estimatedCost,
        'advancePayment': advancePayment,
        'status': status.name,
        'priority': priority.name,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'photoUrl': photoUrl,
        'technicianId': technicianId,
      };

  factory OrderModel.fromMap(Map<String, dynamic> map) => OrderModel(
        id: map['id'] as String,
        customer: Customer.fromMap(map['customer'] as Map<String, dynamic>),
        deviceModel: map['deviceModel'] as String,
        brand: map['brand'] as String?,
        issueDescription: map['issueDescription'] as String,
        estimatedCost: (map['estimatedCost'] as num).toInt(),
        advancePayment: (map['advancePayment'] as num).toInt(),
        status: OrderStatus.values.firstWhere((e) => e.name == map['status']),
        priority: JobPriority.values.firstWhere((e) => e.name == map['priority']),
        createdAt: DateTime.parse(map['createdAt'] as String),
        updatedAt: DateTime.parse(map['updatedAt'] as String),
        photoUrl: map['photoUrl'] as String?,
        technicianId: map['technicianId'] as String?,
      );
}


