import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/types.dart';

final sharedPrefsProvider = FutureProvider<SharedPreferences>((ref) async {
  return SharedPreferences.getInstance();
});

class OrdersNotifier extends StateNotifier<List<OrderModel>> {
  OrdersNotifier() : super(const []) {
    _load();
  }

  static const _key = 'app_orders';

  Future<void> _load() async {
    final prefs = await SharedPreferences.getInstance();
    final raw = prefs.getString(_key);
    if (raw == null || raw.isEmpty) return;
    final list = (jsonDecode(raw) as List)
        .map((e) => OrderModel.fromMap(Map<String, dynamic>.from(e as Map)))
        .toList();
    state = list;
  }

  Future<void> _save() async {
    final prefs = await SharedPreferences.getInstance();
    final list = state.map((o) => o.toMap()).toList();
    await prefs.setString(_key, jsonEncode(list));
  }

  Future<void> add(OrderModel order) async {
    state = [order, ...state];
    await _save();
  }

  Future<void> update(OrderModel order) async {
    state = state.map((o) => o.id == order.id ? order : o).toList();
    await _save();
  }

  Future<void> remove(String id) async {
    state = state.where((o) => o.id != id).toList();
    await _save();
  }
}

final ordersProvider = StateNotifierProvider<OrdersNotifier, List<OrderModel>>(
  (ref) => OrdersNotifier(),
);

final activeTabProvider = StateProvider<int>((ref) => 0);


