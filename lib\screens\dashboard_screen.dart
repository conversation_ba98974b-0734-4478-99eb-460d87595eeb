import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../state/app_state.dart';
import '../models/types.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final orders = ref.watch(ordersProvider);
    final activeRepairs = orders
        .where((o) => o.status != OrderStatus.delivered && o.status != OrderStatus.cancelled)
        .length;
    final ready = orders.where((o) => o.status == OrderStatus.readyForDelivery).length;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _statCard(context, 'Active Repairs', activeRepairs.toString(), Colors.blue),
              _statCard(context, 'Ready for Delivery', ready.toString(), Colors.green),
              _statCard(context, 'Low Stock', '0', Colors.red),
              _statCard(context, "Today's Earnings", '₹0', Colors.purple),
            ],
          ),
          const SizedBox(height: 16),
          Text('Recent Activity', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          if (orders.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('No recent activity.'),
              ),
            )
          else
            Card(
              child: Column(
                children: orders.take(5).map((o) => _activityItem(o, context)).toList(),
              ),
            )
        ],
      ),
    );
  }

  Widget _statCard(BuildContext context, String title, String value, Color color) {
    return SizedBox(
      width: (MediaQuery.of(context).size.width - 16 * 2 - 12) / 2,
      child: Card(
        color: color,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: const TextStyle(color: Colors.white70)),
              const SizedBox(height: 8),
              Text(value, style: const TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _activityItem(OrderModel order, BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.smartphone),
      title: Text(order.deviceModel),
      subtitle: Text(order.customer.name),
      trailing: Text(order.status.name),
      onTap: () {},
    );
  }
}


