import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../state/app_state.dart';
import '../models/types.dart';
import 'order_details_screen.dart';

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({super.key});

  @override
  ConsumerState<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> {
  String filter = '';
  String status = 'Active';

  @override
  Widget build(BuildContext context) {
    final orders = ref.watch(ordersProvider);

    Iterable<OrderModel> filtered = orders;
    switch (status) {
      case 'Active':
        filtered = filtered.where((o) => [
              OrderStatus.received,
              OrderStatus.diagnosing,
              OrderStatus.repairInProgress,
              OrderStatus.qualityCheck,
            ].contains(o.status));
        break;
      case 'Ready':
        filtered = filtered.where((o) => o.status == OrderStatus.readyForDelivery);
        break;
      case 'Completed':
        filtered = filtered.where((o) => o.status == OrderStatus.delivered);
        break;
      case 'Cancelled':
        filtered = filtered.where((o) => o.status == OrderStatus.cancelled);
        break;
      case 'All':
        break;
    }

    if (filter.isNotEmpty) {
      final q = filter.toLowerCase();
      filtered = filtered.where((o) =>
          o.id.toLowerCase().contains(q) ||
          o.deviceModel.toLowerCase().contains(q) ||
          o.customer.name.toLowerCase().contains(q) ||
          o.customer.phone.contains(q));
    }

    final list = filtered.toList();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Repair Orders', style: Theme.of(context).textTheme.titleLarge),
              FilledButton.icon(onPressed: _addDemoOrder, icon: const Icon(Icons.add), label: const Text('New Order')),
            ],
          ),
          const SizedBox(height: 8),
          TextField(
            decoration: const InputDecoration(prefixIcon: Icon(Icons.search), hintText: 'Search...'),
            onChanged: (v) => setState(() => filter = v.trim()),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: ['Active', 'Ready', 'Completed', 'Cancelled', 'All']
                  .map((s) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: ChoiceChip(
                          label: Text(s),
                          selected: status == s,
                          onSelected: (_) => setState(() => status = s),
                        ),
                      ))
                  .toList(),
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: list.isEmpty
                ? const Center(child: Text('No orders found'))
                : ListView.builder(
                    itemCount: list.length,
                    itemBuilder: (context, index) {
                      final o = list[index];
                      return Card(
                        child: ListTile(
                          leading: const Icon(Icons.smartphone),
                          title: Text(o.deviceModel),
                          subtitle: Text('${o.customer.name} • ${o.customer.phone}'),
                          trailing: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(o.status.name),
                              Text('₹${o.estimatedCost}')
                            ],
                          ),
                          onTap: () async {
                            await Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => OrderDetailsScreen(order: o),
                              ),
                            );
                            setState(() {});
                          },
                        ),
                      );
                    },
                  ),
          )
        ],
      ),
    );
  }

  void _addDemoOrder() {
    final notifier = ref.read(ordersProvider.notifier);
    final now = DateTime.now();
    notifier.add(
      OrderModel(
        id: 'ORD-${now.millisecondsSinceEpoch}',
        customer: const Customer(id: 'CUS-1', name: 'Rahul Sharma', phone: '**********'),
        deviceModel: 'iPhone 12',
        brand: 'Apple',
        issueDescription: 'Screen cracked',
        estimatedCost: 12000,
        advancePayment: 2000,
        status: OrderStatus.received,
        priority: JobPriority.normal,
        createdAt: now,
        updatedAt: now,
      ),
    );
  }
}


