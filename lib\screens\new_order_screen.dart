import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/types.dart';
import '../state/app_state.dart';

class NewOrderScreen extends ConsumerStatefulWidget {
  const NewOrderScreen({super.key});
  @override
  ConsumerState<NewOrderScreen> createState() => _NewOrderScreenState();
}

class _NewOrderScreenState extends ConsumerState<NewOrderScreen> {
  final formKey = GlobalKey<FormState>();
  final nameCtrl = TextEditingController();
  final phoneCtrl = TextEditingController();
  final modelCtrl = TextEditingController();
  final issueCtrl = TextEditingController();
  final costCtrl = TextEditingController();
  final advanceCtrl = TextEditingController(text: '0');
  JobPriority priority = JobPriority.normal;

  @override
  void dispose() {
    nameCtrl.dispose();
    phoneCtrl.dispose();
    modelCtrl.dispose();
    issueCtrl.dispose();
    costCtrl.dispose();
    advanceCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('New Job Card')),
      body: Form(
        key: formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: nameCtrl,
              decoration: const InputDecoration(labelText: 'Customer Name'),
              validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
            ),
            TextFormField(
              controller: phoneCtrl,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(labelText: 'Phone'),
              validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
            ),
            TextFormField(
              controller: modelCtrl,
              decoration: const InputDecoration(labelText: 'Device Model'),
              validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
            ),
            TextFormField(
              controller: issueCtrl,
              decoration: const InputDecoration(labelText: 'Issue Description'),
              maxLines: 3,
            ),
            TextFormField(
              controller: costCtrl,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(labelText: 'Estimated Cost (₹)'),
              validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
            ),
            TextFormField(
              controller: advanceCtrl,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(labelText: 'Advance Payment (₹)'),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<JobPriority>(
              value: priority,
              items: JobPriority.values
                  .map((p) => DropdownMenuItem(value: p, child: Text(p.name)))
                  .toList(),
              onChanged: (p) => setState(() => priority = p ?? JobPriority.normal),
              decoration: const InputDecoration(labelText: 'Priority'),
            ),
            const SizedBox(height: 24),
            FilledButton(
              onPressed: _submit,
              child: const Text('Create'),
            )
          ],
        ),
      ),
    );
  }

  Future<void> _submit() async {
    if (!formKey.currentState!.validate()) return;
    final now = DateTime.now();
    final id = 'ORD-${const Uuid().v4()}';
    final customer = Customer(
      id: 'CUS-${const Uuid().v4()}',
      name: nameCtrl.text.trim(),
      phone: phoneCtrl.text.trim(),
    );
    final order = OrderModel(
      id: id,
      customer: customer,
      deviceModel: modelCtrl.text.trim(),
      issueDescription: issueCtrl.text.trim(),
      estimatedCost: int.parse(costCtrl.text.trim()),
      advancePayment: int.tryParse(advanceCtrl.text.trim()) ?? 0,
      status: OrderStatus.received,
      priority: priority,
      createdAt: now,
      updatedAt: now,
    );
    await ref.read(ordersProvider.notifier).add(order);
    if (!mounted) return;
    Navigator.of(context).pop();
  }
}


